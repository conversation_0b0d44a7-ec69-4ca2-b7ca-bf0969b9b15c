package config

import (
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
)

// NotificationConfig extends the base config with notification-specific settings
type NotificationConfig struct {
	*config.Config
	RateLimiting RateLimitingConfig `mapstructure:"rate_limiting"`
	Templates    TemplatesConfig    `mapstructure:"templates"`
}

// RateLimitingConfig defines rate limiting settings
type RateLimitingConfig struct {
	DefaultLimit  int            `mapstructure:"default_limit"`
	WindowMinutes int            `mapstructure:"window_minutes"`
	PerTypeLimits map[string]int `mapstructure:"per_type_limits"`
}

// TemplatesConfig defines template caching settings
type TemplatesConfig struct {
	CacheTTL       time.Duration `mapstructure:"cache_ttl"`
	ReloadInterval time.Duration `mapstructure:"reload_interval"`
}

// Load loads the notification service configuration
func Load() (*NotificationConfig, error) {
	baseConfig, err := config.Load()
	if err != nil {
		return nil, err
	}

	// Create notification config with base config
	notificationConfig := &NotificationConfig{
		Config: baseConfig,
		RateLimiting: RateLimitingConfig{
			DefaultLimit:  10,
			WindowMinutes: 60,
			PerTypeLimits: map[string]int{
				"VOUCHER_EXPIRING":   5,
				"ORDER_CONFIRMATION": 20,
				"VOUCHER_APPLIED":    15,
				"USER_WELCOME":       3,
			},
		},
		Templates: TemplatesConfig{
			CacheTTL:       time.Hour,
			ReloadInterval: 5 * time.Minute,
		},
	}

	return notificationConfig, nil
}

// GetRateLimit returns the rate limit for a specific notification type
func (c *RateLimitingConfig) GetRateLimit(notificationType string) int {
	if limit, exists := c.PerTypeLimits[notificationType]; exists {
		return limit
	}
	return c.DefaultLimit
}
