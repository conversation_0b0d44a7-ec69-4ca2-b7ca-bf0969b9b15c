package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-order-service/internal/repository"
	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type VoucherClient interface {
	CheckVoucherEligibility(ctx context.Context, userID uint64, voucherCode string, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) (*VoucherEligibilityResponse, error)
	GetEligibleAutoVouchers(ctx context.Context, userID uint64, orderAmount float64, orderTimestamp time.Time, items []model.OrderItem) ([]*VoucherEligibilityResponse, error)
	IncrementVoucherUsage(ctx context.Context, voucherID, userID, orderID uint64, orderAmount, discountAmount float64, orderTimestamp time.Time) error
}

type VoucherEligibilityResponse struct {
	Eligible       bool    `json:"eligible"`
	VoucherID      *uint64 `json:"voucher_id"`
	VoucherCode    *string `json:"voucher_code"`
	DiscountAmount float64 `json:"discount_amount"`
	Message        string  `json:"message"`
}

type OrderService interface {
	CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error)
	GetOrder(ctx context.Context, orderID uint64) (*model.Order, error)
	ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error)
	ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error)
	GetUserOrderCount(ctx context.Context, userID uint64) (int64, error)
	GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error)
	UpdateOrderStatus(ctx context.Context, req *model.UpdateOrderStatusRequest) (*model.Order, error)
}

type orderService struct {
	repo           repository.OrderRepository
	voucherClient  VoucherClient
	eventPublisher *OrderEventPublisher
	logger         *logging.Logger
}

func NewOrderService(repo repository.OrderRepository, voucherClient VoucherClient, eventPublisher *OrderEventPublisher, logger *logging.Logger) OrderService {
	return &orderService{
		repo:           repo,
		voucherClient:  voucherClient,
		eventPublisher: eventPublisher,
		logger:         logger,
	}
}

func (s *orderService) CreateOrder(ctx context.Context, req *model.CreateOrderRequest) (*model.Order, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Creating order for user %d with amount %.2f", req.UserID, req.OrderAmount)

	calculation, err := s.calculateOrderDiscount(ctx, req.UserID, req.OrderAmount, req.OrderTimestamp, req.VoucherCode, req.Items)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to calculate discount: %v", err))
	}

	order := model.NewOrder(
		req.UserID,
		req.OrderAmount,
		calculation.AppliedVoucherID,
		calculation.DiscountAmount,
		calculation.Status,
		calculation.Message,
	)

	if err := s.repo.CreateWithItems(ctx, order, req.Items); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create order: %v", err))
	}

	if calculation.AppliedVoucherID != nil && calculation.Status == "SUCCESS" {
		if err := s.voucherClient.IncrementVoucherUsage(ctx, *calculation.AppliedVoucherID, req.UserID, order.ID, req.OrderAmount, calculation.DiscountAmount, req.OrderTimestamp); err != nil {
			log.Errorf("Failed to increment voucher usage for voucher %d: %v", *calculation.AppliedVoucherID, err)
		}
	}

	log.Infof("Order created successfully with ID %d", order.ID)

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishOrderCreatedWithVoucherDetails(context.Background(), order, calculation); err != nil {
				s.logger.Errorf("Failed to publish order events: %v", err)
			}
		}()
	}

	return order, nil
}

func (s *orderService) GetOrder(ctx context.Context, orderID uint64) (*model.Order, error) {
	order, err := s.repo.GetByID(ctx, orderID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("order with id %d not found", orderID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get order: %v", err))
	}
	return order, nil
}

func (s *orderService) ListOrders(ctx context.Context, req *model.ListOrdersRequest) (*model.OrderWithPagination, error) {
	result, err := s.repo.List(ctx, req)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders: %v", err))
	}
	return result, nil
}

func (s *orderService) ListOrdersByVoucher(ctx context.Context, req *model.ListOrdersByVoucherRequest) (*model.OrderWithPagination, error) {
	result, err := s.repo.ListByVoucher(ctx, req)
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to list orders by voucher: %v", err))
	}
	return result, nil
}

func (s *orderService) calculateOrderDiscount(ctx context.Context, userID uint64, orderAmount float64, orderTimestamp time.Time, voucherCode *string, items []model.OrderItem) (*model.OrderCalculationResponse, error) {
	log := s.logger.WithContext(ctx)

	response := &model.OrderCalculationResponse{
		OrderAmount:    orderAmount,
		VoucherCode:    voucherCode,
		DiscountAmount: 0,
		FinalAmount:    orderAmount,
		Status:         "SUCCESS",
		Message:        "No voucher applied",
	}

	var selectedVoucher *VoucherEligibilityResponse
	var err error

	if voucherCode != nil && *voucherCode != "" {
		selectedVoucher, err = s.voucherClient.CheckVoucherEligibility(
			ctx, userID, *voucherCode, orderAmount, orderTimestamp, items)
		if err != nil {
			log.Errorf("Failed to check voucher eligibility: %v", err)
			response.Status = "FAILED"
			response.Message = fmt.Sprintf("Failed to validate voucher: %v", err)
			return response, nil
		}

		if !selectedVoucher.Eligible {
			response.Status = "FAILED"
			response.Message = selectedVoucher.Message
			return response, nil
		}
	} else {
		eligibleVouchers, err := s.voucherClient.GetEligibleAutoVouchers(
			ctx, userID, orderAmount, orderTimestamp, items)
		if err != nil {
			log.Warnf("Failed to get eligible auto vouchers: %v", err)
		} else if len(eligibleVouchers) > 0 {
			var bestDiscount float64
			for _, voucher := range eligibleVouchers {
				if voucher.DiscountAmount > bestDiscount {
					bestDiscount = voucher.DiscountAmount
					selectedVoucher = voucher
				}
			}
		}
	}

	if selectedVoucher != nil && selectedVoucher.Eligible {
		response.AppliedVoucherID = selectedVoucher.VoucherID
		response.AppliedVoucherCode = selectedVoucher.VoucherCode
		response.DiscountAmount = selectedVoucher.DiscountAmount
		response.FinalAmount = orderAmount - selectedVoucher.DiscountAmount
		response.Message = fmt.Sprintf("Voucher '%s' applied successfully", *selectedVoucher.VoucherCode)

		if response.FinalAmount < 0 {
			response.FinalAmount = 0
		}
	}

	return response, nil
}

func (s *orderService) GetUserOrderCount(ctx context.Context, userID uint64) (int64, error) {
	count, err := s.repo.GetUserOrderCount(ctx, userID)
	if err != nil {
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user order count: %v", err))
	}
	return count, nil
}

func (s *orderService) GetUserVoucherUsageCount(ctx context.Context, userID, voucherID uint64) (int32, error) {
	count, err := s.repo.GetUserVoucherUsageCount(ctx, userID, voucherID)
	if err != nil {
		return 0, app_errors.NewInternalError(fmt.Sprintf("failed to get user voucher usage count: %v", err))
	}
	return count, nil
}

func (s *orderService) UpdateOrderStatus(ctx context.Context, req *model.UpdateOrderStatusRequest) (*model.Order, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Updating order %d status to %s", req.OrderID, req.Status)

	currentOrder, err := s.repo.GetByID(ctx, req.OrderID)
	if err != nil {
		return nil, app_errors.NewNotFoundError(fmt.Sprintf("order with id %d not found", req.OrderID))
	}
	oldStatus := currentOrder.CalculationStatus

	if err := s.repo.UpdateStatus(ctx, req.OrderID, req.Status); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to update order status: %v", err))
	}

	updatedOrder, err := s.GetOrder(ctx, req.OrderID)
	if err != nil {
		return nil, err
	}

	if s.eventPublisher != nil {
		go func() {
			if err := s.eventPublisher.PublishOrderStatusChanged(context.Background(), req.OrderID, updatedOrder.UserID, oldStatus, req.Status, "Status updated via API"); err != nil {
				s.logger.Errorf("Failed to publish order status changed event: %v", err)
			}
		}()
	}

	return updatedOrder, nil
}
