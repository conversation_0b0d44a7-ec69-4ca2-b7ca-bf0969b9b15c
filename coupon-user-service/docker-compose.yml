services:
  postgres-user:
    image: postgres:16-alpine
    container_name: postgres-user
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-user_db}
    ports:
      - "5434:5432"
    volumes:
      - postgres-user-data:/var/lib/postgresql/data
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "pg_isready -U ${POSTGRES_USER:-coupon} -d ${POSTGRES_DB:-user_db}",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - coupon-network

  redis-user:
    image: redis:7-alpine
    container_name: redis-user
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6381:6379"
    volumes:
      - redis-user-data:/data
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - coupon-network

  # jaeger:
  #   image: jaegertracing/all-in-one:1.56
  #   container_name: jaeger
  #   restart: unless-stopped
  #   ports:
  #     - "16686:16686"
  #     - "6831:6831/udp"
  #   healthcheck:
  #     test:
  #       - "CMD-SHELL"
  #       - "curl -f http://localhost:16686/health || exit 1"
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s
  #   networks:
  #     - coupon-network

  user-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: user-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-user-service
    depends_on:
      postgres-user:
        condition: service_healthy
      redis-user:
        condition: service_healthy
      # jaeger:
      #   condition: service_started
    env_file:
      - .env
    ports:
      - "8082:8080"
      - "50053:50051"
      - "2114:2112"
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:8080/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - coupon-network

volumes:
  postgres-user-data:
  redis-user-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
